#!/usr/bin/env python3
"""
测试 OAuth 自动化功能
"""

import sys
from oauth_automation import OAuthAutomation

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试 OAuth 自动化基本功能")
    
    # 创建自动化实例
    automation = OAuthAutomation(
        client_id='v',
        tenant_url='https://your-backend.com',  # 替换为实际的后端 URL
        task_id='test-task'
    )
    
    try:
        # 测试 PKCE 生成
        verifier, challenge = automation.generate_pkce_pair()
        print(f"✅ PKCE 生成成功:")
        print(f"   verifier: {verifier[:20]}...")
        print(f"   challenge: {challenge[:20]}...")
        
        # 测试授权 URL 构造
        auth_url = automation.build_authorize_url()
        print(f"✅ 授权 URL 构造成功:")
        print(f"   {auth_url[:80]}...")
        
        # 测试邮箱选择
        email = automation.get_random_email()
        print(f"✅ 随机邮箱选择: {email}")
        
        # 测试浏览器初始化
        print("🚀 测试浏览器初始化...")
        if automation.init_browser():
            print("✅ 浏览器初始化成功")
            
            # 访问测试页面
            automation.driver.get("https://www.google.com")
            print("✅ 页面访问测试成功")
            
            # 清理
            automation.cleanup()
            print("✅ 浏览器清理成功")
        else:
            print("❌ 浏览器初始化失败")
            return False
        
        print("\n🎉 所有基本功能测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_full_automation():
    """测试完整自动化流程"""
    print("\n🧪 测试完整自动化流程")
    print("⚠️ 这将打开浏览器并访问 Augment Code")
    
    response = input("是否继续? (y/n): ")
    if response.lower() != 'y':
        print("测试已取消")
        return
    
    tenant_url = input("请输入你的后端 URL: ")
    if not tenant_url:
        print("❌ 后端 URL 不能为空")
        return
    
    automation = OAuthAutomation(
        client_id='v',
        tenant_url=tenant_url,
        task_id='full-test'
    )
    
    try:
        result = automation.start()
        print("🎉 完整自动化测试成功!")
        print(f"结果: {result}")
        
    except Exception as e:
        print(f"❌ 完整自动化测试失败: {e}")
        print("💡 浏览器保持打开，你可以手动完成流程")

def main():
    print("🧪 OAuth 自动化系统测试")
    print("=" * 40)
    
    # 基本功能测试
    if not test_basic_functionality():
        print("❌ 基本功能测试失败")
        sys.exit(1)
    
    # 完整流程测试 (可选)
    test_full_automation()

if __name__ == '__main__':
    main()
