# OAuth 自动化系统

🤖 使用 undetected-chromedriver 自动化 Augment Code OAuth 注册和授权流程

## ✨ 核心特性

- 🛡️ **undetected-chromedriver**: 专业反检测，绕过 Cloudflare 保护
- 🎯 **智能表单填写**: 自动识别和填写注册表单
- 📧 **Mock 邮箱池**: 内置临时邮箱，无需真实邮箱
- 🔒 **PKCE 支持**: 完整的 OAuth 2.0 流程
- 🌐 **Web 控制台**: 简洁的管理界面

## 🚀 快速开始

```bash
# 1. 安装依赖
./start.sh

# 2. 访问控制台
# http://localhost:4533
```

## 📋 使用步骤

1. **创建任务**: 填写 Client ID 和后端 URL
2. **启动自动化**: 点击开始，系统自动完成注册流程
3. **获取结果**: 查看授权码和 token 结果

## ❓ 常见问题

**Q: Python 依赖安装失败？**
```bash
python3 --version  # 确保 Python 3.7+
python3 -m pip install --upgrade pip
python3 -m pip install -r requirements.txt
```

**Q: Chrome 浏览器问题？**
- macOS: `brew install --cask google-chrome`
- undetected-chromedriver 会自动匹配版本

**Q: 自动化卡住？**
- 查看浏览器窗口，可能需要手动处理验证码

## API 接口

### 创建任务
```http
POST /api/create-task
Content-Type: application/json

{
  "clientId": "v",
  "tenantUrl": "https://你的后端域名"
}
```

### 启动自动化
```http
POST /api/start-automation/:taskId
```

### 查询任务状态
```http
GET /api/task-status/:taskId
```

### 获取所有任务
```http
GET /api/tasks
```

## 核心流程

1. **生成授权参数**: 创建 code_verifier 和 code_challenge
2. **构造授权 URL**: 包含所有必要的 OAuth 参数
3. **浏览器自动化**: 使用 Playwright 控制浏览器
4. **表单自动填写**: 智能识别并填写注册表单
5. **处理验证码**: 等待隐式 CAPTCHA 加载
6. **提取授权码**: 从页面 URL 或内容中获取 code
7. **交换 Token**: 使用授权码换取访问令牌

## 配置说明

### Mock 邮箱池
系统内置多个临时邮箱地址，自动轮换使用：
- <EMAIL>
- <EMAIL>
- <EMAIL>
- 等等...

### 默认密码
所有自动注册账户使用统一密码: `Test1234!`

## 项目结构

```
├── python_server.py           # Flask 服务器
├── oauth_automation.py        # 核心自动化逻辑 (undetected-chromedriver)
├── setup_python.py            # Python 环境设置脚本
├── test_automation.py         # 功能测试脚本
├── requirements.txt           # Python 依赖
├── public/
│   └── index.html             # Web 控制台界面
├── package.json               # npm 脚本配置
└── README.md
```

## 服务端口

- **主服务**: http://localhost:4533 (Web 控制台)

## 核心优势

### 🛡️ **undetected-chromedriver**
- 专门设计用于绕过反机器人检测
- 自动处理 Chrome 版本匹配
- 内置反检测机制，无需额外配置

### 🎯 **智能化程度高**
- 自动识别表单字段
- 人类化操作模拟
- 智能等待和重试机制

### 🔧 **易于维护**
- Python 代码简洁易懂
- 模块化设计
- 详细的日志输出

## 注意事项

- 确保目标 OAuth 服务支持自动化访问
- 某些网站可能有反爬虫机制
- 建议在测试环境中使用
- 遵守相关网站的使用条款
