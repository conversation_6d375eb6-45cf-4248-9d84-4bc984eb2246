#!/usr/bin/env python3
"""
IP 查询管理 - 使用本地梯子 + IPFoxy IP查询
"""

import requests
from typing import Optional, Dict

class IPChecker:
    def __init__(self):
        # IP查询服务列表
        self.ip_services = [
            'http://httpbin.org/ip',
            'https://api.ipify.org?format=json',
            'https://ipinfo.io/json',
            'https://api.myip.com'
        ]

    def get_current_ip(self) -> Optional[Dict]:
        """获取当前IP信息 (通过本地梯子)"""
        try:
            print("🔍 检查当前IP地址...")

            # 尝试多个IP查询服务
            for service in self.ip_services:
                try:
                    response = requests.get(service, timeout=10)
                    if response.status_code == 200:
                        data = response.json()

                        # 统一返回格式
                        if 'origin' in data:  # httpbin.org
                            ip = data['origin']
                        elif 'ip' in data:  # ipify.org, ipinfo.io
                            ip = data['ip']
                        else:
                            ip = str(data)

                        print(f"✅ 当前IP: {ip}")

                        # 获取更详细的IP信息
                        return self.get_ip_details(ip)

                except Exception as e:
                    print(f"⚠️ IP查询服务 {service} 失败: {e}")
                    continue

            print("❌ 所有IP查询服务都失败")
            return None

        except Exception as e:
            print(f"❌ IP查询异常: {e}")
            return None

    def get_ip_details(self, ip: str) -> Dict:
        """获取IP详细信息"""
        try:
            # 使用 ipinfo.io 获取详细信息
            response = requests.get(f'https://ipinfo.io/{ip}/json', timeout=10)
            if response.status_code == 200:
                data = response.json()

                result = {
                    'ip': ip,
                    'country': data.get('country', 'Unknown'),
                    'region': data.get('region', 'Unknown'),
                    'city': data.get('city', 'Unknown'),
                    'org': data.get('org', 'Unknown'),
                    'timezone': data.get('timezone', 'Unknown')
                }

                print(f"🌍 IP详情: {result['country']} - {result['region']} - {result['city']}")
                return result

        except Exception as e:
            print(f"⚠️ IP详情查询失败: {e}")

        # 返回基本信息
        return {
            'ip': ip,
            'country': 'Unknown',
            'region': 'Unknown',
            'city': 'Unknown',
            'org': 'Unknown',
            'timezone': 'Unknown'
        }

    def check_region_access(self) -> bool:
        """检查当前IP是否在允许的地区"""
        try:
            ip_info = self.get_current_ip()
            if not ip_info:
                return False

            # 检查是否在允许的国家/地区
            allowed_countries = ['US', 'CA', 'GB', 'AU', 'DE', 'FR', 'NL', 'SG']
            country = ip_info.get('country', '').upper()

            if country in allowed_countries:
                print(f"✅ 当前地区 {country} 允许访问")
                return True
            else:
                print(f"❌ 当前地区 {country} 可能被限制")
                return False

        except Exception as e:
            print(f"❌ 地区检查失败: {e}")
            return False

# 使用示例
if __name__ == '__main__':
    ip_checker = IPChecker()

    # 检查当前IP
    ip_info = ip_checker.get_current_ip()
    if ip_info:
        print(f"🎉 IP信息: {ip_info}")

        # 检查地区访问权限
        ip_checker.check_region_access()
    else:
        print("❌ 无法获取IP信息")
