#!/usr/bin/env python3
"""
OAuth 自动化服务器 - 使用 undetected-chromedriver
"""

import os
import sys
import json
import uuid
import time
import threading
from datetime import datetime
from flask import Flask, request, jsonify, send_from_directory, send_file
from flask_cors import CORS

from oauth_automation import OAuthAutomation
from proxy_config import IPChecker

app = Flask(__name__, static_folder='public', static_url_path='')
CORS(app)

# 存储任务状态
tasks = {}

@app.route('/')
def index():
    """主页"""
    return send_file('public/index.html')

@app.route('/<path:filename>')
def static_files(filename):
    """静态文件服务"""
    return send_from_directory('public', filename)

@app.route('/api/create-task', methods=['POST'])
def create_task():
    """创建自动化任务"""
    try:
        data = request.get_json()
        task_id = str(uuid.uuid4())

        client_id = data.get('clientId', 'v')
        temp_email_url = data.get('tempEmailUrl', 'http://localhost:3001')

        # 检查当前IP地区
        ip_checker = IPChecker()
        ip_info = ip_checker.get_current_ip()
        if ip_info:
            print(f"🌍 当前IP: {ip_info['ip']} ({ip_info['country']} - {ip_info['city']})")

            # 检查地区访问权限
            if not ip_checker.check_region_access():
                print("⚠️ 当前地区可能被限制，但继续尝试...")
        else:
            print("⚠️ 无法获取IP信息，继续执行...")

        # 创建自动化实例
        automation = OAuthAutomation(
            client_id=client_id,
            task_id=task_id,
            temp_email_url=temp_email_url
        )
        
        tasks[task_id] = {
            'id': task_id,
            'status': 'created',
            'automation': automation,
            'created_at': datetime.now(),
            'result': None,
            'error': None
        }
        
        return jsonify({
            'success': True,
            'taskId': task_id,
            'message': '任务创建成功'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/start-automation/<task_id>', methods=['POST'])
def start_automation(task_id):
    """启动自动化流程"""
    try:
        if task_id not in tasks:
            return jsonify({
                'success': False,
                'error': '任务不存在'
            }), 404
        
        task = tasks[task_id]
        
        if task['status'] == 'running':
            return jsonify({
                'success': False,
                'error': '任务已在运行中'
            })
        
        # 异步执行自动化流程
        def run_automation():
            try:
                task['status'] = 'running'
                result = task['automation'].start()
                task['status'] = 'completed'
                task['result'] = result
            except Exception as e:
                task['status'] = 'failed'
                task['error'] = str(e)
                print(f"❌ 任务 {task_id} 失败: {e}")
        
        thread = threading.Thread(target=run_automation)
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'success': True,
            'message': '自动化流程已启动',
            'taskId': task_id
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/task-status/<task_id>', methods=['GET'])
def get_task_status(task_id):
    """查询任务状态"""
    try:
        if task_id not in tasks:
            return jsonify({
                'success': False,
                'error': '任务不存在'
            }), 404
        
        task = tasks[task_id]
        
        return jsonify({
            'success': True,
            'task': {
                'id': task['id'],
                'status': task['status'],
                'created_at': task['created_at'].isoformat(),
                'result': task['result'],
                'error': task['error']
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/tasks', methods=['GET'])
def get_all_tasks():
    """获取所有任务"""
    try:
        task_list = []
        for task_id, task in tasks.items():
            task_list.append({
                'id': task['id'],
                'status': task['status'],
                'created_at': task['created_at'].isoformat()
            })
        
        return jsonify({
            'success': True,
            'tasks': task_list
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/cleanup/<task_id>', methods=['POST'])
def cleanup_task(task_id):
    """手动清理任务 (关闭浏览器)"""
    try:
        if task_id not in tasks:
            return jsonify({
                'success': False,
                'error': '任务不存在'
            }), 404
        
        task = tasks[task_id]
        
        if hasattr(task['automation'], 'cleanup'):
            task['automation'].cleanup()
            print(f"🧹 任务 {task_id} 的浏览器已手动关闭")
        
        return jsonify({
            'success': True,
            'message': '浏览器已关闭'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/check-ip', methods=['GET'])
def check_ip():
    """检查当前IP信息"""
    try:
        ip_checker = IPChecker()
        ip_info = ip_checker.get_current_ip()

        if ip_info:
            # 检查地区访问权限
            region_allowed = ip_checker.check_region_access()

            return jsonify({
                'success': True,
                'ip_info': ip_info,
                'region_allowed': region_allowed
            })
        else:
            return jsonify({
                'success': False,
                'error': '无法获取IP信息'
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    port = 4533

    print("🚀 OAuth 自动化服务启动中...")
    print("🐍 使用 Python + undetected-chromedriver")
    print(f"🌐 服务地址: http://localhost:{port}")

    app.run(host='0.0.0.0', port=port, debug=True)
