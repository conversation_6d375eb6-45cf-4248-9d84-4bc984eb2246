#!/bin/bash

echo "🚀 OAuth 自动化系统启动脚本"
echo "=" * 40

# 检查 Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 未安装"
    echo "💡 macOS: brew install python"
    echo "💡 Ubuntu: sudo apt install python3 python3-pip"
    exit 1
fi

echo "✅ Python 版本: $(python3 --version)"

# 检查依赖是否已安装
if [ ! -f "requirements.txt" ]; then
    echo "❌ requirements.txt 文件不存在"
    exit 1
fi

# 安装依赖 (如果需要)
echo "📦 检查 Python 依赖..."
python3 -c "import undetected_chromedriver, selenium, flask" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "🔧 安装 Python 依赖..."
    python3 -m pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
    echo "✅ 依赖安装完成"
else
    echo "✅ 依赖已安装"
fi

# 启动服务
echo "🚀 启动 OAuth 自动化服务..."
echo "🌐 访问地址: http://localhost:4533"
echo "🛑 按 Ctrl+C 停止服务"
echo ""

python3 python_server.py
