#!/usr/bin/env python3
"""
临时邮箱 API 服务器 - 基于 IMAP 的邮箱生成和邮件获取
"""

import os
import re
import time
import random
import string
import imaplib
import email
from email.header import decode_header
from flask import Flask, request, jsonify
from flask_cors import CORS
from dotenv import load_dotenv
import threading
from typing import Dict, List, Optional

# 加载环境变量
load_dotenv()

app = Flask(__name__)
CORS(app)

# 配置
IMAP_SERVER = os.getenv('IMAP_SERVER', 'imap.qq.com')
IMAP_PORT = int(os.getenv('IMAP_PORT', '993'))
IMAP_USER = os.getenv('IMAP_USER')
IMAP_PASSWORD = os.getenv('IMAP_PASSWORD')
DOMAIN = os.getenv('DOMAIN', 'jiushen.website')

# 存储生成的邮箱地址
generated_emails: Dict[str, dict] = {}

class TempEmailService:
    def __init__(self):
        self.imap_conn = None
    
    def connect_imap(self):
        """连接到 IMAP 服务器"""
        try:
            self.imap_conn = imaplib.IMAP4_SSL(IMAP_SERVER, IMAP_PORT)
            self.imap_conn.login(IMAP_USER, IMAP_PASSWORD)
            print(f"✅ IMAP 连接成功: {IMAP_USER}")
            return True
        except Exception as e:
            print(f"❌ IMAP 连接失败: {e}")
            return False
    
    def disconnect_imap(self):
        """断开 IMAP 连接"""
        if self.imap_conn:
            try:
                self.imap_conn.close()
                self.imap_conn.logout()
            except:
                pass
    
    def generate_email_address(self, device_id: Optional[str] = None) -> str:
        """生成临时邮箱地址"""
        # 生成随机字符串
        random_part = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))
        
        if device_id:
            email_address = f"{random_part}-device{device_id}@{DOMAIN}"
        else:
            email_address = f"{random_part}@{DOMAIN}"
        
        # 存储邮箱信息
        generated_emails[email_address] = {
            'created_at': time.time(),
            'device_id': device_id,
            'last_checked': 0,
            'emails': []
        }
        
        return email_address
    
    def decode_mime_words(self, s):
        """解码 MIME 编码的字符串"""
        if not s:
            return ""
        
        decoded_parts = []
        for part, encoding in decode_header(s):
            if isinstance(part, bytes):
                if encoding:
                    try:
                        decoded_parts.append(part.decode(encoding))
                    except:
                        decoded_parts.append(part.decode('utf-8', errors='ignore'))
                else:
                    decoded_parts.append(part.decode('utf-8', errors='ignore'))
            else:
                decoded_parts.append(str(part))
        
        return ''.join(decoded_parts)
    
    def extract_verification_code(self, content: str) -> Optional[str]:
        """从邮件内容中提取验证码"""
        if not content:
            return None

        print(f"🔍 邮件内容预览: {content[:300]}...")

        # Augment Code 特定的验证码模式
        patterns = [
            # 匹配 Augment Code 的验证码格式
            r'verification code[:\s]+is[:\s]*([A-Z0-9]{4,8})',
            r'your code[:\s]+is[:\s]*([A-Z0-9]{4,8})',
            r'code[:\s]+is[:\s]*([A-Z0-9]{4,8})',
            r'enter[:\s]+the[:\s]+code[:\s]*([A-Z0-9]{4,8})',
            # 通用验证码模式
            r'verification code[:\s]*([A-Z0-9]{4,8})',
            r'verify code[:\s]*([A-Z0-9]{4,8})',
            r'code[:\s]*([A-Z0-9]{4,8})',
            r'验证码[:\s]*([A-Z0-9]{4,8})',
            # 匹配独立的验证码 (通常在单独一行)
            r'^\s*([A-Z0-9]{4,8})\s*$',
            # 匹配纯数字验证码
            r'\b(\d{4,8})\b',
            # 匹配字母数字组合
            r'\b([A-Z0-9]{6})\b',
        ]

        content_lines = content.split('\n')
        content_upper = content.upper()

        # 排除常见的非验证码词汇
        exclude_words = {
            'WITH', 'FROM', 'THAT', 'THIS', 'YOUR', 'CODE', 'MAIL', 'EMAIL',
            'HTTP', 'HTTPS', 'HTML', 'HREF', 'LINK', 'CLICK', 'HERE', 'BUTTON',
            'STYLE', 'COLOR', 'FONT', 'SIZE', 'TEXT', 'ALIGN', 'CENTER'
        }

        # 首先尝试逐行匹配，寻找独立的验证码
        for line in content_lines:
            line_upper = line.strip().upper()
            if line_upper and len(line_upper) >= 4 and len(line_upper) <= 8:
                if line_upper.isalnum() and line_upper not in exclude_words:
                    # 检查是否包含数字
                    if any(c.isdigit() for c in line_upper):
                        print(f"✅ 从独立行提取验证码: {line_upper}")
                        return line_upper

        # 然后使用正则表达式匹配
        for pattern in patterns:
            matches = re.findall(pattern, content_upper, re.MULTILINE | re.IGNORECASE)
            if matches:
                for match in matches:
                    code = match.strip()
                    # 过滤掉排除词汇和过短的匹配
                    if len(code) >= 4 and code not in exclude_words:
                        # 检查是否是纯数字或字母数字组合
                        if code.isdigit() or (code.isalnum() and any(c.isdigit() for c in code)):
                            print(f"✅ 通过正则提取验证码: {code}")
                            return code

        print("❌ 未找到有效验证码")
        return None
    
    def check_if_email_is_for_address(self, email_message, target_address: str) -> bool:
        """检查邮件是否发送给指定地址"""
        target_address_lower = target_address.lower()
        local_part = target_address.split('@')[0].lower()

        # 检查 To 字段
        to_header = email_message.get('To', '').lower()
        if target_address_lower in to_header:
            print(f"✅ To 字段匹配: {to_header}")
            return True

        # 检查 Delivered-To 字段
        delivered_to = email_message.get('Delivered-To', '').lower()
        if target_address_lower in delivered_to:
            print(f"✅ Delivered-To 字段匹配: {delivered_to}")
            return True

        # 检查 X-Original-To 字段
        x_original_to = email_message.get('X-Original-To', '').lower()
        if target_address_lower in x_original_to:
            print(f"✅ X-Original-To 字段匹配: {x_original_to}")
            return True

        # 检查邮件源中是否包含目标地址
        email_str = str(email_message).lower()
        if target_address_lower in email_str:
            print(f"✅ 邮件源包含目标地址")
            return True

        print(f"❌ 邮件不是发送给 {target_address}")
        return False

    def get_emails_for_address(self, email_address: str) -> List[dict]:
        """获取指定邮箱地址的邮件"""
        if not self.connect_imap():
            return []

        try:
            self.imap_conn.select('INBOX')

            # 搜索最近的邮件 (不限制收件人，后面再过滤)
            status, messages = self.imap_conn.search(None, 'ALL')

            if status != 'OK':
                print(f"❌ 搜索邮件失败: {status}")
                return []

            message_ids = messages[0].split()
            emails = []

            print(f"📧 搜索到 {len(message_ids)} 封邮件，开始过滤...")

            # 获取最近的邮件 (最多50封)
            for msg_id in message_ids[-50:]:
                try:
                    status, msg_data = self.imap_conn.fetch(msg_id, '(RFC822)')
                    if status != 'OK':
                        continue

                    email_body = msg_data[0][1]
                    email_message = email.message_from_bytes(email_body)

                    # 检查邮件是否发送给目标地址
                    if not self.check_if_email_is_for_address(email_message, email_address):
                        continue

                    # 解析邮件信息
                    subject = self.decode_mime_words(email_message.get('Subject', ''))
                    from_addr = self.decode_mime_words(email_message.get('From', ''))
                    date = email_message.get('Date', '')

                    print(f"📧 处理邮件: {subject} (来自: {from_addr})")
                    
                    # 获取邮件内容
                    body = ""
                    html_body = ""
                    
                    if email_message.is_multipart():
                        for part in email_message.walk():
                            content_type = part.get_content_type()
                            if content_type == "text/plain":
                                try:
                                    body = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                                except:
                                    pass
                            elif content_type == "text/html":
                                try:
                                    html_body = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                                except:
                                    pass
                    else:
                        try:
                            body = email_message.get_payload(decode=True).decode('utf-8', errors='ignore')
                        except:
                            body = str(email_message.get_payload())
                    
                    # 提取验证码
                    verification_code = self.extract_verification_code(body) or self.extract_verification_code(html_body)
                    
                    email_data = {
                        'id': msg_id.decode(),
                        'from': from_addr,
                        'subject': subject,
                        'body': body,
                        'html': html_body,
                        'date': date,
                        'verification_code': verification_code,
                        'timestamp': time.time()
                    }
                    
                    emails.append(email_data)
                    
                except Exception as e:
                    print(f"❌ 处理邮件失败: {e}")
                    continue
            
            # 按时间倒序排列
            emails.sort(key=lambda x: x['timestamp'], reverse=True)
            
            return emails
            
        except Exception as e:
            print(f"❌ 获取邮件失败: {e}")
            return []
        finally:
            self.disconnect_imap()

# 创建服务实例
email_service = TempEmailService()

@app.route('/api/generate-email', methods=['POST'])
def generate_email():
    """生成临时邮箱地址"""
    try:
        data = request.get_json() or {}
        device_id = data.get('deviceId')
        
        email_address = email_service.generate_email_address(device_id)
        
        return jsonify({
            'success': True,
            'email': email_address
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/emails', methods=['GET'])
def get_emails():
    """获取邮箱邮件"""
    try:
        email_address = request.args.get('address')
        
        if not email_address:
            return jsonify({
                'success': False,
                'error': '邮箱地址不能为空'
            }), 400
        
        emails = email_service.get_emails_for_address(email_address)
        
        return jsonify(emails)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/test-connection', methods=['GET'])
def test_connection():
    """测试 IMAP 连接"""
    try:
        success = email_service.connect_imap()
        if success:
            email_service.disconnect_imap()
            return jsonify({
                'success': True,
                'message': 'IMAP 连接正常'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'IMAP 连接失败'
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/generated-emails', methods=['GET'])
def list_generated_emails():
    """列出所有生成的邮箱"""
    try:
        emails_list = []
        for email_addr, info in generated_emails.items():
            emails_list.append({
                'address': email_addr,
                'created_at': info['created_at'],
                'device_id': info.get('device_id'),
                'email_count': len(info.get('emails', []))
            })
        
        return jsonify({
            'success': True,
            'emails': emails_list
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    port = int(os.getenv('PORT', 3001))
    
    print("🚀 临时邮箱 API 服务启动中...")
    print(f"📧 IMAP 服务器: {IMAP_SERVER}:{IMAP_PORT}")
    print(f"👤 IMAP 用户: {IMAP_USER}")
    print(f"🌐 邮箱域名: {DOMAIN}")
    print(f"🔗 服务地址: http://localhost:{port}")
    
    # 测试 IMAP 连接
    if email_service.connect_imap():
        email_service.disconnect_imap()
        print("✅ IMAP 连接测试成功")
    else:
        print("❌ IMAP 连接测试失败，请检查配置")
    
    app.run(host='0.0.0.0', port=port, debug=True)
