#!/usr/bin/env python3
"""
临时邮箱客户端 - 基于现有临时邮箱项目的 Python 接口
"""

import requests
import time
import re
import json
from typing import Optional, List, Dict, Any

class TempEmailClient:
    def __init__(self, base_url: str = "http://localhost:3001", domain: str = "jiushen.website"):
        """
        初始化临时邮箱客户端
        
        Args:
            base_url: 临时邮箱服务的基础URL
            domain: 默认邮箱域名
        """
        self.base_url = base_url.rstrip('/')
        self.domain = domain
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'TempEmailClient/1.0'
        })
    
    def generate_email(self, device_id: Optional[str] = None, domain: Optional[str] = None) -> Optional[str]:
        """
        生成临时邮箱地址
        
        Args:
            device_id: 设备ID (可选)
            domain: 邮箱域名 (可选，默认使用初始化时的域名)
            
        Returns:
            生成的邮箱地址，失败返回 None
        """
        try:
            url = f"{self.base_url}/api/generate-email"
            payload = {
                "domain": domain or self.domain
            }
            
            if device_id:
                payload["deviceId"] = device_id
            
            response = self.session.post(url, json=payload, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            email = data.get('email')
            
            if email:
                print(f"✅ 生成临时邮箱: {email}")
                return email
            else:
                print("❌ 邮箱生成失败: 响应中没有邮箱地址")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 生成邮箱请求失败: {e}")
            return None
        except Exception as e:
            print(f"❌ 生成邮箱异常: {e}")
            return None
    
    def get_emails(self, email_address: str) -> List[Dict[str, Any]]:
        """
        获取指定邮箱的邮件列表
        
        Args:
            email_address: 邮箱地址
            
        Returns:
            邮件列表，每个邮件包含 id, from, subject, body, timestamp, verificationCode 等字段
        """
        try:
            url = f"{self.base_url}/api/emails"
            params = {"address": email_address}
            
            response = self.session.get(url, params=params, timeout=15)
            response.raise_for_status()
            
            emails = response.json()
            
            if isinstance(emails, list):
                print(f"✅ 获取到 {len(emails)} 封邮件")
                return emails
            else:
                print("❌ 邮件获取失败: 响应格式错误")
                return []
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 获取邮件请求失败: {e}")
            return []
        except Exception as e:
            print(f"❌ 获取邮件异常: {e}")
            return []
    
    def extract_verification_code(self, email_content: str) -> Optional[str]:
        """
        从邮件内容中提取验证码

        Args:
            email_content: 邮件内容 (text 或 html)

        Returns:
            提取到的验证码，未找到返回 None
        """
        if not email_content:
            return None

        print(f"🔍 邮件内容预览: {email_content[:200]}...")

        # 更精确的验证码模式
        patterns = [
            r'verification code[:\s]+is[:\s]*([A-Z0-9]{4,8})',
            r'verification code[:\s]*([A-Z0-9]{4,8})',
            r'verify code[:\s]*([A-Z0-9]{4,8})',
            r'code[:\s]*is[:\s]*([A-Z0-9]{4,8})',
            r'code[:\s]*([A-Z0-9]{4,8})',
            r'验证码[:\s]*([A-Z0-9]{4,8})',
            r'\b(\d{4,8})\b',  # 4-8位纯数字
            r'\b([A-Z0-9]{6})\b',  # 6位字母数字组合
        ]

        content_upper = email_content.upper()

        # 排除常见的非验证码词汇
        exclude_words = {'WITH', 'FROM', 'THAT', 'THIS', 'YOUR', 'CODE', 'MAIL', 'EMAIL', 'HTTP', 'HTTPS', 'HTML'}

        for pattern in patterns:
            matches = re.findall(pattern, content_upper, re.IGNORECASE)
            if matches:
                for match in matches:
                    code = match.strip()
                    # 过滤掉排除词汇和过短的匹配
                    if len(code) >= 4 and code not in exclude_words:
                        # 检查是否是纯数字或字母数字组合
                        if code.isdigit() or (code.isalnum() and any(c.isdigit() for c in code)):
                            print(f"✅ 客户端提取到验证码: {code}")
                            return code

        print("❌ 客户端未找到有效验证码")
        return None
    
    def wait_for_verification_code(self, email_address: str, timeout: int = 60, check_interval: int = 3) -> Optional[str]:
        """
        等待并获取验证码
        
        Args:
            email_address: 邮箱地址
            timeout: 超时时间 (秒)
            check_interval: 检查间隔 (秒)
            
        Returns:
            验证码，超时或失败返回 None
        """
        print(f"📧 等待邮箱 {email_address} 的验证码...")
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            emails = self.get_emails(email_address)
            
            if emails:
                # 检查最新的邮件
                for email in emails:
                    # 优先使用服务器提取的验证码
                    if email.get('verificationCode'):
                        code = email['verificationCode']
                        print(f"✅ 从服务器获取验证码: {code}")
                        return code
                    
                    # 备用：从邮件内容中提取
                    body = email.get('body', '')
                    html = email.get('html', '')
                    
                    # 先尝试从文本内容提取
                    code = self.extract_verification_code(body)
                    if code:
                        print(f"✅ 从邮件文本提取验证码: {code}")
                        return code
                    
                    # 再尝试从HTML内容提取
                    code = self.extract_verification_code(html)
                    if code:
                        print(f"✅ 从邮件HTML提取验证码: {code}")
                        return code
            
            print(f"⏳ 等待验证码... ({int(time.time() - start_time)}/{timeout}s)")
            time.sleep(check_interval)
        
        print("⏰ 等待验证码超时")
        return None
    
    def test_connection(self) -> bool:
        """
        测试与临时邮箱服务的连接

        Returns:
            连接成功返回 True，失败返回 False
        """
        try:
            # 使用专门的测试接口
            url = f"{self.base_url}/api/test-connection"
            response = self.session.get(url, timeout=10)
            response.raise_for_status()

            data = response.json()
            if data.get('success'):
                print(f"✅ 临时邮箱服务连接正常: {data.get('message')}")
                return True
            else:
                print(f"❌ 临时邮箱服务连接失败: {data.get('error')}")
                return False

        except Exception as e:
            print(f"❌ 临时邮箱服务连接异常: {e}")
            return False

# 使用示例
if __name__ == "__main__":
    # 创建客户端
    client = TempEmailClient()
    
    # 测试连接
    if client.test_connection():
        # 生成邮箱
        email = client.generate_email(device_id="oauth-test")
        
        if email:
            # 等待验证码
            code = client.wait_for_verification_code(email, timeout=30)
            
            if code:
                print(f"🎉 成功获取验证码: {code}")
            else:
                print("❌ 未能获取验证码")
    else:
        print("❌ 临时邮箱服务不可用")
