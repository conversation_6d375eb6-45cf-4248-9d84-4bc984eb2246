#!/usr/bin/env python3
"""
Python 环境设置脚本
"""

import os
import sys
import subprocess

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description}完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def main():
    print("🐍 OAuth 自动化系统 - Python 环境设置")
    print("=" * 50)
    
    # 检查 Python 版本
    python_version = sys.version_info
    print(f"🐍 Python 版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 7):
        print("❌ 需要 Python 3.7 或更高版本")
        sys.exit(1)
    
    # 升级 pip
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "升级 pip"):
        print("⚠️ pip 升级失败，继续安装...")
    
    # 安装依赖
    if not run_command(f"{sys.executable} -m pip install -r requirements.txt", "安装 Python 依赖"):
        print("❌ 依赖安装失败")
        sys.exit(1)
    
    print("\n🎉 Python 环境设置完成!")
    print("\n📋 使用说明:")
    print("1. 启动服务: python3 python_server.py")
    print("2. 访问控制台: http://localhost:3000")
    print("3. 创建任务并开始自动化")
    
    print("\n🔧 其他命令:")
    print("- 测试自动化: python3 test_automation.py")
    print("- 手动清理: 关闭浏览器窗口")

if __name__ == '__main__':
    main()
