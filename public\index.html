<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OAuth 自动化控制台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .main-content {
            padding: 40px;
        }
        
        .control-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .panel {
            background: #f8fafc;
            border-radius: 8px;
            padding: 25px;
            border: 1px solid #e2e8f0;
        }
        
        .panel h3 {
            color: #1e293b;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #374151;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }
        
        .btn {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: transform 0.2s, box-shadow 0.2s;
            width: 100%;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 70, 229, 0.3);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .status-panel {
            background: #f8fafc;
            border-radius: 8px;
            padding: 25px;
            border: 1px solid #e2e8f0;
        }
        
        .task-item {
            background: white;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #4f46e5;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        
        .task-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .task-id {
            font-family: monospace;
            background: #f1f5f9;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-created { background: #dbeafe; color: #1e40af; }
        .status-running { background: #fef3c7; color: #92400e; }
        .status-completed { background: #d1fae5; color: #065f46; }
        .status-failed { background: #fee2e2; color: #dc2626; }
        
        .log-container {
            background: #1e293b;
            color: #e2e8f0;
            border-radius: 6px;
            padding: 20px;
            font-family: monospace;
            font-size: 13px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
        
        @media (max-width: 768px) {
            .control-panel {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .main-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 OAuth 自动化控制台</h1>
            <p>自动化 Augment Code OAuth 注册流程，包含邮箱验证码处理</p>
        </div>
        
        <div class="main-content">
            <div class="control-panel">
                <div class="panel">
                    <h3>🚀 创建自动化任务</h3>
                    <div class="form-group">
                        <label for="clientId">Client ID</label>
                        <input type="text" id="clientId" value="v" placeholder="输入 Client ID">
                    </div>
                    <div class="info-box" style="background: #e8f5e8; border: 1px solid #4caf50; padding: 10px; margin: 10px 0; border-radius: 4px;">
                        <strong>🛡️ 反检测技术:</strong> 随机指纹伪装 + WebGL伪装 + 地理位置伪装<br>
                        <strong>📧 真实邮箱:</strong> 基于 IMAP 的临时邮箱服务，自动获取真实验证码<br>
                        <strong>🌐 网络环境:</strong> 本地梯子 + IP地区检查
                    </div>

                    <div id="ip-status" class="info-box" style="background: #f0f0f0; border: 1px solid #ccc; padding: 10px; margin: 10px 0; border-radius: 4px;">
                        <strong>🔍 IP状态:</strong> <span id="ip-info">检查中...</span>
                        <button onclick="checkIP()" style="margin-left: 10px; padding: 2px 8px; font-size: 12px;">刷新</button>
                    </div>

                    <div id="ip-status" class="info-box" style="background: #f0f0f0; border: 1px solid #ccc; padding: 10px; margin: 10px 0; border-radius: 4px;">
                        <strong>🔍 IP状态:</strong> <span id="ip-info">检查中...</span>
                        <button onclick="checkIP()" style="margin-left: 10px; padding: 2px 8px; font-size: 12px;">刷新</button>
                    </div>
                    <button class="btn" onclick="createTask()">创建任务</button>
                </div>
                
                <div class="panel">
                    <h3>⚡ 快速操作</h3>
                    <div class="form-group">
                        <label for="taskId">任务 ID</label>
                        <input type="text" id="taskId" placeholder="输入任务 ID">
                    </div>
                    <button class="btn" onclick="startAutomation()" style="margin-bottom: 10px;">开始自动化</button>
                    <button class="btn" onclick="checkStatus()" style="background: linear-gradient(135deg, #059669 0%, #047857 100%); margin-bottom: 10px;">检查状态</button>
                    <button class="btn" onclick="cleanupTask()" style="background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);">关闭浏览器</button>
                </div>
            </div>
            
            <div class="status-panel">
                <h3>📊 任务状态</h3>
                <div id="taskList">
                    <p style="text-align: center; color: #6b7280; padding: 20px;">暂无任务</p>
                </div>
                <button class="btn" onclick="refreshTasks()" style="background: linear-gradient(135deg, #0891b2 0%, #0e7490 100%); margin-top: 20px;">刷新任务列表</button>
            </div>
            
            <div class="log-container" id="logContainer">
                <div>🎯 OAuth 自动化控制台已就绪</div>
                <div>💡 点击"创建任务"开始自动化流程</div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:4533/api';
        
        function log(message) {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            logContainer.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        async function createTask() {
            const clientId = document.getElementById('clientId').value;

            if (!clientId) {
                log('❌ 请输入 Client ID');
                return;
            }

            try {
                log('🚀 正在创建任务...');
                log('🛡️ 使用 undetected-chromedriver 反检测技术');

                const response = await fetch(`${API_BASE}/create-task`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        clientId
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    log(`✅ 任务创建成功: ${result.taskId}`);
                    document.getElementById('taskId').value = result.taskId;
                    refreshTasks();
                } else {
                    log(`❌ 任务创建失败: ${result.error}`);
                }
            } catch (error) {
                log(`❌ 请求失败: ${error.message}`);
            }
        }
        
        async function startAutomation() {
            const taskId = document.getElementById('taskId').value;
            
            if (!taskId) {
                log('❌ 请输入任务 ID');
                return;
            }
            
            try {
                log(`⚡ 启动自动化流程: ${taskId}`);
                const response = await fetch(`${API_BASE}/start-automation/${taskId}`, {
                    method: 'POST'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    log(`✅ 自动化流程已启动`);
                    refreshTasks();
                } else {
                    log(`❌ 启动失败: ${result.error}`);
                    if (result.error.includes('浏览器未安装')) {
                        log(`💡 解决方案: 在终端运行 "npm run install-browsers"`);
                    }
                }
            } catch (error) {
                log(`❌ 请求失败: ${error.message}`);
            }
        }
        
        async function checkStatus() {
            const taskId = document.getElementById('taskId').value;
            
            if (!taskId) {
                log('❌ 请输入任务 ID');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/task-status/${taskId}`);
                const result = await response.json();
                
                if (result.success) {
                    log(`📊 任务状态: ${result.task.status}`);
                    if (result.task.result) {
                        log(`🎉 执行结果: ${JSON.stringify(result.task.result, null, 2)}`);
                    }
                    if (result.task.error) {
                        log(`❌ 错误信息: ${result.task.error}`);
                    }
                } else {
                    log(`❌ 查询失败: ${result.error}`);
                }
            } catch (error) {
                log(`❌ 请求失败: ${error.message}`);
            }
        }
        
        async function cleanupTask() {
            const taskId = document.getElementById('taskId').value;

            if (!taskId) {
                log('❌ 请输入任务 ID');
                return;
            }

            try {
                log(`🧹 关闭任务浏览器: ${taskId}`);
                const response = await fetch(`${API_BASE}/cleanup/${taskId}`, {
                    method: 'POST'
                });

                const result = await response.json();

                if (result.success) {
                    log(`✅ 浏览器已关闭`);
                } else {
                    log(`❌ 关闭失败: ${result.error}`);
                }
            } catch (error) {
                log(`❌ 请求失败: ${error.message}`);
            }
        }

        async function refreshTasks() {
            try {
                const response = await fetch(`${API_BASE}/tasks`);
                const result = await response.json();

                if (result.success) {
                    displayTasks(result.tasks);
                    log(`🔄 任务列表已刷新 (${result.tasks.length} 个任务)`);
                } else {
                    log(`❌ 刷新失败: ${result.error}`);
                }
            } catch (error) {
                log(`❌ 请求失败: ${error.message}`);
            }
        }
        
        function displayTasks(tasks) {
            const taskList = document.getElementById('taskList');
            
            if (tasks.length === 0) {
                taskList.innerHTML = '<p style="text-align: center; color: #6b7280; padding: 20px;">暂无任务</p>';
                return;
            }
            
            taskList.innerHTML = tasks.map(task => `
                <div class="task-item">
                    <div class="task-header">
                        <span class="task-id">${task.id}</span>
                        <span class="status-badge status-${task.status}">${task.status}</span>
                    </div>
                    <div style="color: #6b7280; font-size: 14px;">
                        创建时间: ${new Date(task.createdAt).toLocaleString()}
                    </div>
                </div>
            `).join('');
        }

        // IP检查功能
        async function checkIP() {
            try {
                document.getElementById('ip-info').textContent = '检查中...';

                const response = await fetch(`${API_BASE}/check-ip`);
                const data = await response.json();

                if (data.success) {
                    const ipInfo = data.ip_info;
                    const regionAllowed = data.region_allowed;

                    const statusIcon = regionAllowed ? '✅' : '⚠️';
                    const statusText = regionAllowed ? '允许访问' : '可能受限';

                    document.getElementById('ip-info').innerHTML =
                        `${statusIcon} ${ipInfo.ip} (${ipInfo.country} - ${ipInfo.city}) - ${statusText}`;
                } else {
                    document.getElementById('ip-info').textContent = `❌ ${data.error}`;
                }
            } catch (error) {
                document.getElementById('ip-info').textContent = `❌ 检查失败: ${error.message}`;
            }
        }

        // 页面加载时刷新任务列表和检查IP
        window.addEventListener('load', function() {
            refreshTasks();
            checkIP();
            log('🎯 OAuth 自动化控制台已就绪');
            log('💡 点击"创建任务"开始自动化流程');
        });
    </script>
</body>
</html>
