#!/bin/bash

echo "🚀 OAuth 自动化系统 + 临时邮箱服务启动脚本"
echo "=" * 50

# 检查 Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 未安装"
    exit 1
fi

echo "✅ Python 版本: $(python3 --version)"

# 安装依赖
echo "📦 安装 Python 依赖..."
python3 -m pip install -r requirements.txt

# 检查 .env 文件
if [ ! -f ".env" ]; then
    echo "❌ .env 文件不存在，请先配置 IMAP 设置"
    exit 1
fi

echo "✅ 配置文件检查完成"

# 启动临时邮箱服务 (后台运行)
echo "📧 启动临时邮箱服务..."
python3 temp_email_server.py &
TEMP_EMAIL_PID=$!

# 等待临时邮箱服务启动
sleep 3

# 启动 OAuth 自动化服务
echo "🤖 启动 OAuth 自动化服务..."
echo "🌐 OAuth 控制台: http://localhost:4533"
echo "📧 临时邮箱 API: http://localhost:3001"
echo "🛑 按 Ctrl+C 停止所有服务"
echo ""

# 启动主服务
python3 python_server.py

# 清理：当主服务停止时，也停止临时邮箱服务
echo "🧹 清理后台服务..."
kill $TEMP_EMAIL_PID 2>/dev/null
