#!/usr/bin/env python3
"""
OAuth 自动化核心逻辑 - 使用 undetected-chromedriver
"""

import os
import time
import random
import hashlib
import base64
import secrets
import requests
from urllib.parse import urlencode, urlparse, parse_qs
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
import undetected_chromedriver as uc
from temp_email_client import TempEmailClient
import json

class OAuthAutomation:
    def __init__(self, client_id='v', task_id=None, temp_email_url="http://localhost:3001"):
        self.client_id = client_id
        self.task_id = task_id
        self.driver = None

        # 初始化临时邮箱客户端
        self.temp_email_client = TempEmailClient(base_url=temp_email_url)
        self.current_email = None

        self.default_password = 'Test1234!'

    def generate_random_fingerprint(self):
        """生成随机浏览器指纹"""
        import random

        # 随机屏幕分辨率
        screen_resolutions = [
            (1920, 1080), (1366, 768), (1440, 900), (1536, 864),
            (1280, 720), (1600, 900), (1024, 768), (1280, 1024)
        ]
        width, height = random.choice(screen_resolutions)

        # 随机时区
        timezones = [
            'America/New_York', 'America/Los_Angeles', 'Europe/London',
            'Europe/Paris', 'Asia/Tokyo', 'Australia/Sydney',
            'America/Chicago', 'Europe/Berlin', 'Asia/Shanghai'
        ]
        timezone = random.choice(timezones)

        # 随机语言
        languages = [
            'en-US,en;q=0.9',
            'en-GB,en;q=0.9',
            'fr-FR,fr;q=0.9,en;q=0.8',
            'de-DE,de;q=0.9,en;q=0.8',
            'es-ES,es;q=0.9,en;q=0.8'
        ]
        language = random.choice(languages)

        # 随机 WebGL 参数
        webgl_vendor = random.choice([
            'Google Inc. (NVIDIA)',
            'Google Inc. (Intel)',
            'Google Inc. (AMD)',
            'Google Inc. (Apple)'
        ])

        webgl_renderer = random.choice([
            'ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 Direct3D11 vs_5_0 ps_5_0, D3D11)',
            'ANGLE (Intel, Intel(R) HD Graphics 620 Direct3D11 vs_5_0 ps_5_0, D3D11)',
            'ANGLE (AMD, AMD Radeon RX 580 Direct3D11 vs_5_0 ps_5_0, D3D11)',
            'Apple GPU'
        ])

        return {
            'screen_width': width,
            'screen_height': height,
            'timezone': timezone,
            'language': language,
            'webgl_vendor': webgl_vendor,
            'webgl_renderer': webgl_renderer,
            'hardware_concurrency': random.choice([2, 4, 6, 8, 12, 16]),
            'device_memory': random.choice([2, 4, 8, 16, 32]),
            'platform': random.choice(['Win32', 'MacIntel', 'Linux x86_64'])
        }

    def create_stealth_script(self, fingerprint):
        """创建反检测脚本"""
        return f"""
        // 覆盖 WebGL 指纹
        const getParameter = WebGLRenderingContext.prototype.getParameter;
        WebGLRenderingContext.prototype.getParameter = function(parameter) {{
            if (parameter === 37445) {{
                return '{fingerprint['webgl_vendor']}';
            }}
            if (parameter === 37446) {{
                return '{fingerprint['webgl_renderer']}';
            }}
            return getParameter.call(this, parameter);
        }};

        // 覆盖 WebGL2 指纹
        if (typeof WebGL2RenderingContext !== 'undefined') {{
            const getParameter2 = WebGL2RenderingContext.prototype.getParameter;
            WebGL2RenderingContext.prototype.getParameter = function(parameter) {{
                if (parameter === 37445) {{
                    return '{fingerprint['webgl_vendor']}';
                }}
                if (parameter === 37446) {{
                    return '{fingerprint['webgl_renderer']}';
                }}
                return getParameter2.call(this, parameter);
            }};
        }}

        // 覆盖屏幕信息
        Object.defineProperty(screen, 'width', {{
            get: () => {fingerprint['screen_width']}
        }});
        Object.defineProperty(screen, 'height', {{
            get: () => {fingerprint['screen_height']}
        }});
        Object.defineProperty(screen, 'availWidth', {{
            get: () => {fingerprint['screen_width']}
        }});
        Object.defineProperty(screen, 'availHeight', {{
            get: () => {fingerprint['screen_height'] - 40}
        }});

        // 覆盖硬件信息
        Object.defineProperty(navigator, 'hardwareConcurrency', {{
            get: () => {fingerprint['hardware_concurrency']}
        }});

        if ('deviceMemory' in navigator) {{
            Object.defineProperty(navigator, 'deviceMemory', {{
                get: () => {fingerprint['device_memory']}
            }});
        }}

        // 覆盖平台信息
        Object.defineProperty(navigator, 'platform', {{
            get: () => '{fingerprint['platform']}'
        }});

        // 覆盖语言信息
        Object.defineProperty(navigator, 'language', {{
            get: () => '{fingerprint['language'].split(',')[0]}'
        }});
        Object.defineProperty(navigator, 'languages', {{
            get: () => {json.dumps(fingerprint['language'].split(','))}
        }});

        // 移除 webdriver 标识
        Object.defineProperty(navigator, 'webdriver', {{
            get: () => undefined
        }});

        // 覆盖 Chrome 对象
        if ('chrome' in window) {{
            Object.defineProperty(window, 'chrome', {{
                get: () => ({{
                    runtime: {{}}
                }})
            }});
        }}

        // 覆盖权限查询
        const originalQuery = window.navigator.permissions.query;
        window.navigator.permissions.query = (parameters) => (
            parameters.name === 'notifications' ?
                Promise.resolve({{ state: Notification.permission }}) :
                originalQuery(parameters)
        );

        // 移除自动化相关属性
        delete window.navigator.__proto__.webdriver;
        delete window.navigator.webdriver;

        // 覆盖插件信息
        Object.defineProperty(navigator, 'plugins', {{
            get: () => [1, 2, 3, 4, 5]
        }});

        // 覆盖 mimeTypes
        Object.defineProperty(navigator, 'mimeTypes', {{
            get: () => [1, 2, 3, 4]
        }});

        // 伪造 iframe 检测
        const originalCreateElement = document.createElement;
        document.createElement = function(tagName) {{
            const element = originalCreateElement.call(this, tagName);
            if (tagName === 'iframe') {{
                try {{
                    if (element.contentWindow) {{
                        element.contentWindow.navigator = window.navigator;
                    }}
                }} catch (e) {{}}
            }}
            return element;
        }};

        // 覆盖 toString 方法
        const originalToString = Function.prototype.toString;
        Function.prototype.toString = function() {{
            if (this === navigator.webdriver) {{
                return 'function webdriver() {{ [native code] }}';
            }}
            return originalToString.call(this);
        }};

        console.log('🛡️ 增强浏览器指纹伪装已激活');
        """
    
    def base64url_encode(self, data):
        """Base64URL 编码"""
        return base64.urlsafe_b64encode(data).decode('utf-8').rstrip('=')
    
    def generate_pkce_pair(self):
        """生成 PKCE verifier 和 challenge"""
        # 生成 code_verifier
        verifier = base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
        
        # 生成 code_challenge
        challenge = base64.urlsafe_b64encode(
            hashlib.sha256(verifier.encode('utf-8')).digest()
        ).decode('utf-8').rstrip('=')
        
        return verifier, challenge
    
    def build_authorize_url(self):
        """构造授权 URL"""
        verifier, challenge = self.generate_pkce_pair()
        state = secrets.token_urlsafe(32)
        
        self.verifier = verifier
        self.state = state
        
        params = {
            'response_type': 'code',
            'code_challenge': challenge,
            'client_id': self.client_id,
            'state': state,
            'prompt': 'login'
        }
        
        return f"https://auth.augmentcode.com/authorize?{urlencode(params)}"
    
    def generate_temp_email(self):
        """生成临时邮箱"""
        try:
            # 使用任务ID作为设备ID，确保邮箱唯一性
            device_id = self.task_id or f"oauth-{int(time.time())}"

            email = self.temp_email_client.generate_email(device_id=device_id)
            if email:
                self.current_email = email
                print(f"✅ 生成临时邮箱成功: {email}")
                return email
            else:
                print("❌ 临时邮箱生成失败，使用备用邮箱")
                # 备用方案：生成一个简单的测试邮箱
                backup_email = f"test{int(time.time())}@jiushen.website"
                self.current_email = backup_email
                return backup_email

        except Exception as e:
            print(f"❌ 临时邮箱生成异常: {e}")
            # 备用方案
            backup_email = f"test{int(time.time())}@jiushen.website"
            self.current_email = backup_email
            return backup_email
    
    def init_browser(self):
        """初始化 undetected-chromedriver (简化模式)"""
        try:
            print("🚀 启动 undetected-chromedriver...")

            # undetected-chromedriver 选项
            options = uc.ChromeOptions()

            # 基本必要选项 (最小化配置)
            options.add_argument('--no-first-run')
            options.add_argument('--no-default-browser-check')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-blink-features=AutomationControlled')

            # 窗口大小 (使用常见分辨率)
            options.add_argument('--window-size=1280,720')

            # 注意: undetected-chromedriver 已经内置了大部分反检测功能
            # 过多的自定义可能反而暴露自动化特征
            
            # 创建 undetected Chrome 实例
            self.driver = uc.Chrome(
                options=options,
                version_main=None,  # 自动检测 Chrome 版本
                driver_executable_path=None,  # 自动下载 chromedriver
                browser_executable_path=None,  # 使用系统 Chrome
                user_data_dir=None,  # 使用临时用户数据目录
                headless=False,  # 必须为 False，undetected 在 headless 模式下效果不好
                use_subprocess=True,
                debug=False,
                keep_alive=True  # 保持连接活跃
            )

            # 设置页面加载超时
            self.driver.set_page_load_timeout(30)
            self.driver.implicitly_wait(10)

            print("✅ undetected-chromedriver 初始化成功")
            return True
            
        except Exception as e:
            print(f"❌ 浏览器初始化失败: {e}")
            return False
    
    def simulate_human_behavior(self):
        """快速模式 - 跳过人类行为模拟"""
        print("⚡ 快速模式：跳过人类行为模拟")
        time.sleep(0.5)  # 最小等待

    def wait_for_cloudflare_challenge(self, max_wait=20):
        """快速检查 Cloudflare 挑战"""
        try:
            print("🛡️ 快速检查 Cloudflare 状态...")

            start_time = time.time()

            while time.time() - start_time < max_wait:
                current_url = self.driver.current_url
                page_source = self.driver.page_source

                # 检查是否有 Cloudflare 挑战
                cloudflare_indicators = [
                    "600010",
                    "challenge",
                    "cf-challenge",
                    "cloudflare"
                ]

                has_challenge = any(indicator in page_source.lower() or indicator in current_url.lower()
                                  for indicator in cloudflare_indicators)

                if has_challenge:
                    print(f"⏳ Cloudflare 挑战中... ({int(time.time() - start_time)}s)")
                    time.sleep(2)  # 快速检查间隔
                else:
                    print("✅ Cloudflare 检查通过")
                    return True

            print("⏰ Cloudflare 等待超时，继续执行")
            return True  # 超时也继续执行

        except Exception as e:
            print(f"❌ Cloudflare 检查失败: {e}")
            return True  # 出错也继续执行
    
    def fast_type(self, element, text):
        """快速输入"""
        try:
            element.clear()
            time.sleep(0.05)
            element.send_keys(text)  # 直接输入整个文本
            return True

        except Exception as e:
            print(f"❌ 人类化输入失败: {e}")
            return False

    def fast_type(self, element, text):
        """快速输入 (机器人验证通过后使用)"""
        try:
            # 直接设置 value 属性，最快的方式
            self.driver.execute_script("arguments[0].value = arguments[1];", element, text)

            # 触发必要的事件
            self.driver.execute_script("""
                var element = arguments[0];
                var events = ['input', 'change', 'blur'];
                events.forEach(function(eventType) {
                    var event = new Event(eventType, { bubbles: true });
                    element.dispatchEvent(event);
                });
            """, element)

            print(f"✅ 快速输入完成: {text}")
            return True

        except Exception as e:
            print(f"❌ 快速输入失败: {e}")
            return False
    
    def check_captcha_status(self):
        """检查机器人验证状态"""
        try:
            # 检查常见的 CAPTCHA 完成标识
            captcha_indicators = [
                'input[name="captcha"]',
                '[data-captcha-solved="true"]',
                '.captcha-solved',
                '[aria-label*="verified" i]'
            ]

            for selector in captcha_indicators:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        element = elements[0]
                        if selector == 'input[name="captcha"]':
                            # 检查 captcha token 是否已填充
                            value = element.get_attribute('value')
                            if value and len(value) > 10:
                                print("✅ 检测到 CAPTCHA token 已填充")
                                return True
                        else:
                            print("✅ 检测到机器人验证完成标识")
                            return True
                except:
                    continue

            return False

        except Exception as e:
            print(f"⚠️ CAPTCHA 状态检查失败: {e}")
            return False

    def fill_registration_form(self, email, password):
        """填写注册表单"""
        try:
            print("📝 填写注册表单...")

            # 检查浏览器窗口状态
            try:
                current_url = self.driver.current_url
                print(f"📍 表单页面: {current_url}")
            except Exception as e:
                print(f"❌ 浏览器窗口已关闭: {e}")
                return False

            # 等待页面加载
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # 先模拟人类行为，然后等待机器人验证
            print("🤖 模拟人类行为并等待机器人验证...")
            self.simulate_human_behavior()

            # 等待机器人验证完成
            max_wait = 30  # 最多等待30秒
            wait_count = 0

            while wait_count < max_wait:
                if self.check_captcha_status():
                    print("✅ 机器人验证已完成，使用快速输入模式")
                    use_fast_input = True
                    break
                time.sleep(1)
                wait_count += 1
                if wait_count % 5 == 0:  # 每5秒打印一次
                    print(f"⏳ 等待机器人验证... ({wait_count}/{max_wait})")
            else:
                print("⚠️ 机器人验证超时，使用人类化输入模式")
                use_fast_input = False

            # 查找邮箱字段
            email_selectors = [
                'input[name="username"]',  # Augment Code 使用的字段
                'input[name="email"]',
                'input[type="email"]',
                'input[placeholder*="email" i]',
                'input[id="username"]'
            ]

            email_filled = False
            for selector in email_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element.is_displayed() and element.is_enabled():
                        print(f"✅ 找到邮箱字段: {selector}")

                        # 快速输入模式
                        success = self.fast_type(element, email)

                        if success:
                            print(f"✅ 邮箱填写成功: {email}")
                            email_filled = True
                            break
                except:
                    continue

            if not email_filled:
                print("❌ 未找到可用的邮箱字段")
                return False

            # 检查是否有密码字段
            password_inputs = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="password"]')
            if password_inputs:
                print(f"🔐 找到 {len(password_inputs)} 个密码字段")
                for i, pwd_input in enumerate(password_inputs):
                    if pwd_input.is_displayed() and pwd_input.is_enabled():
                        self.fast_type(pwd_input, password)
                        print(f"✅ 密码字段 {i+1} 填写成功")
            else:
                print("ℹ️ 未检测到密码字段 (Augment Code 使用邮箱验证流程)")

            return True

        except Exception as e:
            print(f"❌ 填写表单失败: {e}")
            return False
    
    def submit_form(self):
        """提交表单"""
        try:
            print("📤 提交注册表单...")

            # 查找提交按钮
            submit_selectors = [
                'button[type="submit"]',
                'button:contains("Continue")',
                'button:contains("注册")',
                'button:contains("Register")',
                'input[type="submit"]',
                'button[class*="submit"]',
                'button[class*="continue"]'
            ]

            for selector in submit_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element.is_displayed() and element.is_enabled():
                        print(f"✅ 找到提交按钮: {selector}")

                        # 直接点击，不再模拟人类行为
                        element.click()

                        print("✅ 表单提交成功")
                        return True
                except:
                    continue

            # 如果没找到按钮，尝试按 Enter 键提交
            try:
                from selenium.webdriver.common.keys import Keys
                body = self.driver.find_element(By.TAG_NAME, "body")
                body.send_keys(Keys.RETURN)
                print("✅ 使用 Enter 键提交表单")
                return True
            except:
                pass

            print("❌ 未找到可用的提交方式")
            return False

        except Exception as e:
            print(f"❌ 提交表单失败: {e}")
            return False

    def handle_email_verification(self):
        """处理邮箱验证码"""
        try:
            print("📧 检测到邮箱验证码页面...")

            # 等待验证码输入框出现
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, 'input[name="code"]'))
            )

            # 查找验证码输入框
            code_input = self.driver.find_element(By.CSS_SELECTOR, 'input[name="code"]')

            if not code_input.is_displayed() or not code_input.is_enabled():
                print("❌ 验证码输入框不可用")
                return False

            print("✅ 找到验证码输入框")

            # 从临时邮箱获取真实验证码
            if self.current_email:
                print(f"📧 从临时邮箱获取验证码: {self.current_email}")

                # 快速获取验证码 (最多等待30秒)
                verification_code = self.temp_email_client.wait_for_verification_code(
                    self.current_email,
                    timeout=30,  # 减少等待时间
                    check_interval=2  # 加快检查频率
                )

                if verification_code:
                    print(f"✅ 获取到真实验证码: {verification_code}")

                    # 清空输入框并输入验证码
                    code_input.clear()
                    time.sleep(0.5)
                    self.fast_type(code_input, verification_code)

                    # 等待一下再提交
                    time.sleep(1)

                    # 查找并点击继续按钮
                    continue_button = self.driver.find_element(By.CSS_SELECTOR, 'button[type="submit"]')
                    if continue_button.is_displayed() and continue_button.is_enabled():
                        continue_button.click()
                        print("✅ 真实验证码提交成功")

                        # 快速检查页面响应
                        time.sleep(1)

                        # 检查是否有验证码错误提示
                        try:
                            error_selectors = [
                                '.ulp-input-error-message',
                                '[class*="error"]',
                                '[id*="error"]',
                                '.error',
                                '.invalid'
                            ]

                            has_error = False
                            for selector in error_selectors:
                                error_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                                for error_el in error_elements:
                                    if error_el.is_displayed():
                                        error_text = error_el.text.lower()
                                        if 'invalid' in error_text or 'code is invalid' in error_text:
                                            print(f"❌ 验证码错误: {error_el.text}")
                                            has_error = True
                                            break
                                if has_error:
                                    break

                            if has_error:
                                print("🔄 验证码错误，尝试重新获取...")

                                # 快速等待新邮件
                                time.sleep(2)

                                # 重新获取验证码
                                new_code = self.temp_email_client.wait_for_verification_code(
                                    self.current_email,
                                    timeout=30,
                                    check_interval=2
                                )

                                if new_code and new_code != verification_code:
                                    print(f"🔄 获取到新验证码: {new_code}")
                                    code_input.clear()
                                    time.sleep(0.5)
                                    self.fast_type(code_input, new_code)
                                    time.sleep(1)
                                    continue_button.click()
                                    print("✅ 新验证码已提交")

                                    # 快速检查错误
                                    time.sleep(1)
                                    return not self.check_verification_error()
                                else:
                                    print("❌ 未获取到新验证码")
                                    return False
                            else:
                                print("✅ 验证码提交成功，无错误提示")
                                return True

                        except Exception as e:
                            print(f"⚠️ 错误检查失败: {e}")
                            return True  # 假设成功

                        return True
                    else:
                        print("❌ 继续按钮不可用")
                        return False
                else:
                    print("❌ 未能获取验证码，使用备用方案")
                    # 备用方案：使用模拟验证码
                    mock_code = "123456"
                    print(f"🔢 使用备用验证码: {mock_code}")
                    self.fast_type(code_input, mock_code)

                    continue_button = self.driver.find_element(By.CSS_SELECTOR, 'button[type="submit"]')
                    if continue_button.is_displayed() and continue_button.is_enabled():
                        continue_button.click()
                        print("⚠️ 备用验证码已提交 (可能需要手动处理)")
                        return True

            else:
                print("❌ 没有当前邮箱地址")
                return False

        except Exception as e:
            print(f"❌ 邮箱验证码处理失败: {e}")
            return False

    def check_verification_error(self) -> bool:
        """检查是否有验证码错误"""
        try:
            error_selectors = [
                '.ulp-input-error-message',
                '[class*="error"]',
                '[id*="error"]',
                '.error',
                '.invalid'
            ]

            for selector in error_selectors:
                error_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for error_el in error_elements:
                    if error_el.is_displayed():
                        error_text = error_el.text.lower()
                        if 'invalid' in error_text or 'code is invalid' in error_text:
                            return True
            return False
        except:
            return False

    def handle_terms_acceptance(self):
        """处理服务条款确认页面"""
        try:
            print("📋 检测到服务条款确认页面...")

            # 等待页面加载
            time.sleep(2)

            # 检查是否是服务条款页面
            welcome_text = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'Welcome to Augment Code')]")
            terms_checkbox = self.driver.find_elements(By.CSS_SELECTOR, 'input[name="terms-of-service"]')

            if not welcome_text or not terms_checkbox:
                print("ℹ️ 不是服务条款页面，跳过")
                return True

            print("✅ 确认是服务条款页面")

            # 查找并勾选服务条款复选框
            checkbox = terms_checkbox[0]
            if not checkbox.is_selected():
                print("☑️ 勾选服务条款复选框...")

                # 使用 JavaScript 点击，避免被其他元素遮挡
                self.driver.execute_script("arguments[0].click();", checkbox)
                time.sleep(1)

                # 验证是否已勾选
                if checkbox.is_selected():
                    print("✅ 服务条款复选框已勾选")
                else:
                    print("❌ 服务条款复选框勾选失败")
                    return False
            else:
                print("✅ 服务条款复选框已经勾选")

            # 查找并点击注册按钮
            signup_selectors = [
                '#signup-button',
                'button[id="signup-button"]',
                '.sign-link',
                'button:contains("Sign up")',
                'button[type="button"]'
            ]

            signup_clicked = False
            for selector in signup_selectors:
                try:
                    signup_buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for button in signup_buttons:
                        if button.is_displayed() and button.is_enabled():
                            button_text = button.text.lower()
                            if 'sign up' in button_text or 'signup' in button_text:
                                print(f"🚀 点击注册按钮: {button.text}")

                                # 使用 JavaScript 点击
                                self.driver.execute_script("arguments[0].click();", button)
                                signup_clicked = True
                                break
                    if signup_clicked:
                        break
                except:
                    continue

            if signup_clicked:
                print("✅ 注册按钮点击成功")
                time.sleep(3)  # 等待页面跳转
                return True
            else:
                print("❌ 未找到可用的注册按钮")
                return False

        except Exception as e:
            print(f"❌ 服务条款处理失败: {e}")
            return False

    def wait_for_authorization_code(self):
        """等待授权码"""
        try:
            print("⏳ 等待授权流程完成...")

            # 等待页面跳转，最多等待2分钟
            max_attempts = 120
            attempt = 0

            while attempt < max_attempts:
                time.sleep(1)
                attempt += 1

                current_url = self.driver.current_url
                print(f"📍 检查 URL ({attempt}/{max_attempts}): {current_url}")

                # 从 URL 中提取授权码
                try:
                    parsed_url = urlparse(current_url)
                    query_params = parse_qs(parsed_url.query)

                    if 'code' in query_params:
                        code = query_params['code'][0]
                        print(f"✅ 从 URL 获取授权码: {code}")
                        return code

                except Exception as e:
                    pass

                # 检查页面内容中的授权码
                try:
                    page_source = self.driver.page_source

                    # 多种模式匹配
                    import re
                    patterns = [
                        r'"code":\s*"([^"]+)"',
                        r"'code':\s*'([^']+)'",
                        r'code=([^&\s]+)',
                        r'authorization_code[\'\":\s]*([a-zA-Z0-9_-]+)'
                    ]

                    for pattern in patterns:
                        match = re.search(pattern, page_source)
                        if match:
                            code = match.group(1)
                            print(f"✅ 从页面内容获取授权码: {code}")
                            return code

                except Exception as e:
                    pass

                # 检查是否有错误信息
                try:
                    error_elements = self.driver.find_elements(By.CSS_SELECTOR, '.error, .alert-danger, [class*="error"]')
                    if error_elements:
                        for error_el in error_elements:
                            if error_el.is_displayed():
                                error_text = error_el.text
                                print(f"⚠️ 检测到错误信息: {error_text}")
                except:
                    pass

            print("⏰ 等待时间结束，但浏览器保持打开用于调试")
            print("💡 你可以手动完成后续步骤")
            return None

        except Exception as e:
            print(f"❌ 等待授权码失败: {e}")
            return None



    def start(self):
        """主要的自动化流程"""
        try:
            print(f"🚀 开始自动化 OAuth 流程 - 任务 ID: {self.task_id}")

            # 1. 生成授权 URL
            authorize_url = self.build_authorize_url()
            print(f"🔗 授权 URL: {authorize_url}")

            # 2. 初始化浏览器
            if not self.init_browser():
                raise Exception("浏览器初始化失败")

            # 3. 访问授权页面
            print("🌐 访问授权页面...")
            self.driver.get(authorize_url)

            # 快速等待页面加载
            print("⚡ 快速加载页面...")
            time.sleep(3)  # 减少等待时间

            # 检查浏览器窗口状态
            try:
                current_url = self.driver.current_url
                print(f"📍 当前页面: {current_url}")

                # 快速检查 Cloudflare
                self.wait_for_cloudflare_challenge()

            except Exception as e:
                print(f"❌ 浏览器窗口检查失败: {e}")
                raise Exception("浏览器窗口意外关闭")

            # 4. 生成临时邮箱
            email = self.generate_temp_email()
            print(f"📧 使用临时邮箱: {email}")

            # 5. 填写表单
            if not self.fill_registration_form(email, self.default_password):
                raise Exception("表单填写失败")

            # 6. 提交表单
            if not self.submit_form():
                raise Exception("表单提交失败")

            # 7. 处理邮箱验证码 (如果需要)
            time.sleep(2)  # 等待页面跳转

            # 检查是否进入邮箱验证码页面
            try:
                code_input = self.driver.find_element(By.CSS_SELECTOR, 'input[name="code"]')
                if code_input.is_displayed():
                    print("📧 检测到邮箱验证码页面")
                    if not self.handle_email_verification():
                        raise Exception("邮箱验证码处理失败")
            except:
                print("ℹ️ 未检测到邮箱验证码页面，继续流程")

            # 8. 处理服务条款确认页面 (如果需要)
            time.sleep(2)  # 等待页面跳转

            # 检查是否进入服务条款页面
            try:
                welcome_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'Welcome to Augment Code')]")
                if welcome_elements:
                    print("📋 检测到服务条款确认页面")
                    if not self.handle_terms_acceptance():
                        raise Exception("服务条款处理失败")
            except Exception as e:
                print(f"ℹ️ 服务条款处理: {e}")

            # 9. 等待授权码
            auth_code = self.wait_for_authorization_code()
            if not auth_code:
                raise Exception("获取授权码失败")

            # 10. 交换 token (暂时跳过，因为没有后端URL)
            print("ℹ️ 跳过 token 交换步骤 (无后端URL配置)")

            print("🎉 自动化流程完成!")

            return {
                'success': True,
                'email': email,
                'auth_code': auth_code,
                'task_id': self.task_id,
                'message': '自动化流程完成，获取到授权码'
            }

        except Exception as e:
            print(f"❌ 自动化流程失败: {e}")
            print("🔧 调试模式：浏览器保持打开")

            # 检查浏览器是否还存在
            try:
                if self.driver:
                    current_url = self.driver.current_url
                    print(f"🔍 当前页面状态: {current_url}")
                    print("💡 浏览器窗口仍然打开，可以手动继续操作")
            except:
                print("⚠️ 浏览器窗口已关闭")

            raise e

    def cleanup(self):
        """清理资源"""
        try:
            if self.driver:
                self.driver.quit()
                print("🧹 浏览器已关闭")
        except Exception as e:
            print(f"❌ 清理资源失败: {e}")

    def get_driver(self):
        """获取浏览器实例 (用于调试)"""
        return self.driver
